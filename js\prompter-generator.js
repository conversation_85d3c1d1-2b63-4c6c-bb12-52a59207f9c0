// Prompter Generator Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const prompterSourceImage = document.getElementById('prompterSourceImage');
    const prompterImagePreview = document.getElementById('prompterImagePreview');
    const prompterImagePlaceholder = document.getElementById('prompterImagePlaceholder');
    const removePrompterImageBtn = document.getElementById('removePrompterImageBtn');
    const selectPrompterImageBtn = document.getElementById('selectPrompterImageBtn');
    const prompterImageInput = document.getElementById('prompterImageInput');

    // Structured prompt elements
    const prompterSubject = document.getElementById('prompterSubject');
    const prompterAction = document.getElementById('prompterAction');
    const prompterEnvironment = document.getElementById('prompterEnvironment');
    const prompterCamera = document.getElementById('prompterCamera');
    const prompterTime = document.getElementById('prompterTime');
    const prompterMood = document.getElementById('prompterMood');
    const prompterStyle = document.getElementById('prompterStyle');

    // Other UI elements
    const prompterCountSlider = document.getElementById('prompterCountSlider');
    const prompterCountInput = document.getElementById('prompterCountInput');
    const prompterCountValue = document.getElementById('prompterCountValue');
    const prompterLengthSlider = document.getElementById('prompterLengthSlider');
    const prompterLengthInput = document.getElementById('prompterLengthInput');
    const prompterLengthValue = document.getElementById('prompterLengthValue');
    const prompterInstructions = document.getElementById('prompterInstructions');
    const generatePromptsBtn = document.getElementById('generatePromptsBtn');
    const prompterTable = document.getElementById('prompterTable');
    const prompterLoading = document.getElementById('prompterLoading');
    const copyAllPromptsBtn = document.getElementById('copyAllPromptsBtn');
    const clearPromptsBtn = document.getElementById('clearPromptsBtn');
    const exportPromptsBtn = document.getElementById('exportPromptsBtn');

    // Check if elements exist
    if (!generatePromptsBtn) {
        console.warn('Prompter generator elements not found');
        return;
    }

    // Custom elements storage
    let customSubjects = [];
    let customActions = [];
    let customEnvironments = [];
    let customCameras = [];
    let customTimes = [];
    let customMoods = [];
    let customStyles = [];

    // Load custom options from localStorage
    function loadCustomOptions() {
        try {
            // Helper function to load and sort custom items
            function loadAndSortCustomItems(storageKey) {
                const savedItems = localStorage.getItem(storageKey);
                if (savedItems) {
                    const items = JSON.parse(savedItems);
                    // Sort items alphabetically by name
                    items.sort((a, b) => a.name.localeCompare(b.name));
                    return items;
                }
                return [];
            }

            // Load and sort all custom items
            customSubjects = loadAndSortCustomItems('csvision_custom_subjects');
            customActions = loadAndSortCustomItems('csvision_custom_actions');
            customEnvironments = loadAndSortCustomItems('csvision_custom_environments');
            customCameras = loadAndSortCustomItems('csvision_custom_cameras');
            customTimes = loadAndSortCustomItems('csvision_custom_times');
            customMoods = loadAndSortCustomItems('csvision_custom_moods');
            customStyles = loadAndSortCustomItems('csvision_custom_styles');

            // First populate dropdowns with options from structured data
            populateDropdownsFromData();

            // Then update all dropdowns with custom items
            updateDropdown(prompterSubject, customSubjects);
            updateDropdown(prompterAction, customActions);
            updateDropdown(prompterEnvironment, customEnvironments);
            updateDropdown(prompterCamera, customCameras);
            updateDropdown(prompterTime, customTimes);
            updateDropdown(prompterMood, customMoods);
            updateDropdown(prompterStyle, customStyles);

            // Setup delete buttons for custom items
            setupDeleteButtons();

            console.log('Custom options loaded and sorted successfully');
        } catch (error) {
            console.error('Error loading custom options:', error);
        }
    }

    // Populate dropdowns with options from structured data
    function populateDropdownsFromData() {
        if (window.structuredPromptData) {
            populateDropdown(prompterSubject, window.structuredPromptData.subjectOptions);
            populateDropdown(prompterAction, window.structuredPromptData.actionOptions);
            populateDropdown(prompterEnvironment, window.structuredPromptData.environmentOptions);
            populateDropdown(prompterCamera, window.structuredPromptData.cameraOptions);
            populateDropdown(prompterTime, window.structuredPromptData.timeOptions);
            populateDropdown(prompterMood, window.structuredPromptData.moodOptions);
            populateDropdown(prompterStyle, window.structuredPromptData.styleOptions);
        } else {
            console.error('Structured prompt data not found');
        }
    }

    // Populate a dropdown with options
    function populateDropdown(dropdown, options) {
        if (!dropdown) return;

        // Clear existing options except the first one (placeholder)
        while (dropdown.options.length > 1) {
            dropdown.remove(1);
        }

        // Add options
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.label;
            dropdown.appendChild(optionElement);
        });
    }

    // Save custom items to localStorage
    function saveCustomItems(key, items) {
        localStorage.setItem(key, JSON.stringify(items));
    }

    // Update dropdown with custom items
    function updateDropdown(dropdown, customItems) {
        if (!dropdown) return;

        // Remove existing custom options
        Array.from(dropdown.options)
            .filter(option => option.dataset.custom === 'true')
            .forEach(option => dropdown.removeChild(option));

        // Add custom items
        customItems.forEach(item => {
            const option = document.createElement('option');
            option.value = 'custom_' + item.id;
            option.textContent = item.name;
            option.dataset.custom = 'true';
            option.dataset.itemId = item.id;

            // Insert before the "Custom..." option
            const customOption = Array.from(dropdown.options)
                .find(opt => opt.value === 'custom');

            if (customOption) {
                dropdown.insertBefore(option, customOption);
            } else {
                dropdown.appendChild(option);
            }
        });
    }

    // Add a new custom item
    function addCustomItem(dropdown, customItems, storageKey, name) {
        if (!name || name.trim() === '') return;

        const trimmedName = name.trim();

        // Check if an item with the same name already exists
        const existingItem = customItems.find(item => item.name.toLowerCase() === trimmedName.toLowerCase());
        if (existingItem) {
            showToast(`"${trimmedName}" already exists in the list`, 'warning');
            // Select the existing item instead
            dropdown.value = 'custom_' + existingItem.id;
            return existingItem;
        }

        const id = Date.now().toString();
        const newItem = {
            id: id,
            name: trimmedName
        };

        customItems.push(newItem);

        // Sort items alphabetically by name
        customItems.sort((a, b) => a.name.localeCompare(b.name));

        saveCustomItems(storageKey, customItems);
        updateDropdown(dropdown, customItems);

        // Get the type from dropdown ID
        const type = dropdown.id.replace('prompter', '');

        // Make sure delete button is added
        addDeleteButtonToCustomInputGroup(type);

        // Select the newly added item
        dropdown.value = 'custom_' + id;

        return newItem;
    }

    // Add custom subject
    function addCustomSubject(name) {
        return addCustomItem(prompterSubject, customSubjects, 'csvision_custom_subjects', name);
    }

    // Add custom action
    function addCustomAction(name) {
        return addCustomItem(prompterAction, customActions, 'csvision_custom_actions', name);
    }

    // Add custom environment
    function addCustomEnvironment(name) {
        return addCustomItem(prompterEnvironment, customEnvironments, 'csvision_custom_environments', name);
    }

    // Add custom camera work
    function addCustomCamera(name) {
        return addCustomItem(prompterCamera, customCameras, 'csvision_custom_cameras', name);
    }

    // Add custom time of day
    function addCustomTime(name) {
        return addCustomItem(prompterTime, customTimes, 'csvision_custom_times', name);
    }

    // Add custom mood
    function addCustomMood(name) {
        return addCustomItem(prompterMood, customMoods, 'csvision_custom_moods', name);
    }

    // Add custom style
    function addCustomStyle(name) {
        return addCustomItem(prompterStyle, customStyles, 'csvision_custom_styles', name);
    }

    // Delete a custom item
    function deleteCustomItem(dropdown, customItems, storageKey, itemId) {
        if (!itemId) return false;

        // Find the item index
        const itemIndex = customItems.findIndex(item => item.id === itemId);
        if (itemIndex === -1) return false;

        // Get the item name for the toast message
        const itemName = customItems[itemIndex].name;

        // Remove the item from the array
        customItems.splice(itemIndex, 1);

        // Save the updated items
        saveCustomItems(storageKey, customItems);

        // Update the dropdown
        updateDropdown(dropdown, customItems);

        // Show success message
        showToast(`Deleted "${itemName}" successfully`, 'success');

        return true;
    }

    // Delete custom subject
    function deleteCustomSubject(itemId) {
        return deleteCustomItem(prompterSubject, customSubjects, 'csvision_custom_subjects', itemId);
    }

    // Delete custom action
    function deleteCustomAction(itemId) {
        return deleteCustomItem(prompterAction, customActions, 'csvision_custom_actions', itemId);
    }

    // Delete custom environment
    function deleteCustomEnvironment(itemId) {
        return deleteCustomItem(prompterEnvironment, customEnvironments, 'csvision_custom_environments', itemId);
    }

    // Delete custom camera work
    function deleteCustomCamera(itemId) {
        return deleteCustomItem(prompterCamera, customCameras, 'csvision_custom_cameras', itemId);
    }

    // Delete custom time of day
    function deleteCustomTime(itemId) {
        return deleteCustomItem(prompterTime, customTimes, 'csvision_custom_times', itemId);
    }

    // Delete custom mood
    function deleteCustomMood(itemId) {
        return deleteCustomItem(prompterMood, customMoods, 'csvision_custom_moods', itemId);
    }

    // Delete custom style
    function deleteCustomStyle(itemId) {
        return deleteCustomItem(prompterStyle, customStyles, 'csvision_custom_styles', itemId);
    }

    // Get UI elements for structured prompt elements
    const customSubjectInput = document.getElementById('customSubjectInput');
    const customActionInput = document.getElementById('customActionInput');
    const customEnvironmentInput = document.getElementById('customEnvironmentInput');
    const customCameraInput = document.getElementById('customCameraInput');
    const customTimeInput = document.getElementById('customTimeInput');
    const customMoodInput = document.getElementById('customMoodInput');
    const customStyleInput = document.getElementById('customStyleInput');

    // Get custom input buttons
    const addCustomSubjectBtn = document.getElementById('addCustomSubjectBtn');
    const addCustomActionBtn = document.getElementById('addCustomActionBtn');
    const addCustomEnvironmentBtn = document.getElementById('addCustomEnvironmentBtn');
    const addCustomCameraBtn = document.getElementById('addCustomCameraBtn');
    const addCustomTimeBtn = document.getElementById('addCustomTimeBtn');
    const addCustomMoodBtn = document.getElementById('addCustomMoodBtn');
    const addCustomStyleBtn = document.getElementById('addCustomStyleBtn');

    // Get cancel buttons
    const cancelCustomSubjectBtn = document.getElementById('cancelCustomSubjectBtn');
    const cancelCustomActionBtn = document.getElementById('cancelCustomActionBtn');
    const cancelCustomEnvironmentBtn = document.getElementById('cancelCustomEnvironmentBtn');
    const cancelCustomCameraBtn = document.getElementById('cancelCustomCameraBtn');
    const cancelCustomTimeBtn = document.getElementById('cancelCustomTimeBtn');
    const cancelCustomMoodBtn = document.getElementById('cancelCustomMoodBtn');
    const cancelCustomStyleBtn = document.getElementById('cancelCustomStyleBtn');

    // Get custom input groups
    const customSubjectInputGroup = document.getElementById('customSubjectInputGroup');
    const customActionInputGroup = document.getElementById('customActionInputGroup');
    const customEnvironmentInputGroup = document.getElementById('customEnvironmentInputGroup');
    const customCameraInputGroup = document.getElementById('customCameraInputGroup');
    const customTimeInputGroup = document.getElementById('customTimeInputGroup');
    const customMoodInputGroup = document.getElementById('customMoodInputGroup');
    const customStyleInputGroup = document.getElementById('customStyleInputGroup');

    // Add delete buttons to custom input groups
    function setupDeleteButtons() {
        addDeleteButtonToCustomInputGroup('Subject');
        addDeleteButtonToCustomInputGroup('Action');
        addDeleteButtonToCustomInputGroup('Environment');
        addDeleteButtonToCustomInputGroup('Camera');
        addDeleteButtonToCustomInputGroup('Time');
        addDeleteButtonToCustomInputGroup('Mood');
        addDeleteButtonToCustomInputGroup('Style');
    }

    // Function to add delete button to existing custom input groups
    function addDeleteButtonToCustomInputGroup(type) {
        const customInputGroup = document.getElementById(`custom${type}InputGroup`);
        if (!customInputGroup) return;

        // Check if delete button already exists
        const existingDeleteBtn = document.getElementById(`deleteCustom${type}Btn`);
        if (existingDeleteBtn) return;

        // Create delete button
        const deleteBtn = document.createElement('button');
        deleteBtn.id = `deleteCustom${type}Btn`;
        deleteBtn.className = 'btn btn-sm btn-danger';
        deleteBtn.title = `Delete custom ${type.toLowerCase()}`;
        deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';

        // Get the cancel button to insert after
        const cancelBtn = document.getElementById(`cancelCustom${type}Btn`);
        if (cancelBtn) {
            // Insert delete button after cancel button
            cancelBtn.parentNode.insertBefore(deleteBtn, cancelBtn.nextSibling);

            // Setup delete button click handler
            deleteBtn.addEventListener('click', function() {
                // Show delete confirmation dialog
                const customSelect = document.getElementById(`prompter${type}`);
                if (customSelect && customSelect.selectedIndex > 0) {
                    const selectedOption = customSelect.options[customSelect.selectedIndex];
                    if (selectedOption.dataset.custom === 'true') {
                        const itemId = selectedOption.dataset.itemId;
                        const itemName = selectedOption.textContent;

                        if (confirm(`Are you sure you want to delete "${itemName}"?`)) {
                            // Call the appropriate delete function
                            switch(type) {
                                case 'Subject':
                                    deleteCustomSubject(itemId);
                                    break;
                                case 'Action':
                                    deleteCustomAction(itemId);
                                    break;
                                case 'Environment':
                                    deleteCustomEnvironment(itemId);
                                    break;
                                case 'Camera':
                                    deleteCustomCamera(itemId);
                                    break;
                                case 'Time':
                                    deleteCustomTime(itemId);
                                    break;
                                case 'Mood':
                                    deleteCustomMood(itemId);
                                    break;
                                case 'Style':
                                    deleteCustomStyle(itemId);
                                    break;
                            }
                        }
                    } else {
                        showToast('Please select a custom item to delete', 'warning');
                    }
                } else {
                    showToast('Please select a custom item to delete', 'warning');
                }
            });
        }
    }

    // Get other UI elements
    const exportControlsBtn = document.getElementById('exportControlsBtn');
    const importControlsBtn = document.getElementById('importControlsBtn');
    const controlsFileInput = document.getElementById('controlsFileInput');
    const contentTypeImage = document.getElementById('contentTypeImage');
    const contentTypeVideo = document.getElementById('contentTypeVideo');

    // Content type state
    let currentContentType = 'image'; // Default to image

    // Handle content type buttons
    if (contentTypeImage && contentTypeVideo) {
        contentTypeImage.addEventListener('click', function() {
            setContentType('image');
        });

        contentTypeVideo.addEventListener('click', function() {
            setContentType('video');
        });

        // Function to set content type
        function setContentType(type) {
            currentContentType = type;

            if (type === 'image') {
                contentTypeImage.classList.add('btn-primary-purple', 'text-white', 'active');
                contentTypeImage.classList.remove('btn-outline-primary-purple');
                contentTypeVideo.classList.add('btn-outline-primary-purple');
                contentTypeVideo.classList.remove('btn-primary-purple', 'text-white', 'active');
            } else {
                contentTypeVideo.classList.add('btn-primary-purple', 'text-white', 'active');
                contentTypeVideo.classList.remove('btn-outline-primary-purple');
                contentTypeImage.classList.add('btn-outline-primary-purple');
                contentTypeImage.classList.remove('btn-primary-purple', 'text-white', 'active');
            }
        }
    }

    // Setup dropdown change handlers
    function setupDropdownChangeHandler(dropdown, customInputGroup, customInput) {
        if (!dropdown || !customInputGroup || !customInput) return;

        dropdown.addEventListener('change', function() {
            if (this.value === 'custom') {
                // Show custom input field group
                customInputGroup.classList.remove('d-none');
                customInput.focus();

                // Revert to previous selection until user confirms
                this.value = this.dataset.lastValue || '';
            } else {
                // Hide custom input field group
                customInputGroup.classList.add('d-none');

                // Store the current value for future reference
                this.dataset.lastValue = this.value;
            }
        });
    }



    // Setup cancel button handlers
    function setupCancelButtonHandler(cancelBtn, customInputGroup, customInput) {
        if (!cancelBtn || !customInputGroup || !customInput) return;

        cancelBtn.addEventListener('click', function() {
            // Hide custom input field group
            customInputGroup.classList.add('d-none');
            customInput.value = '';
        });
    }

    // Setup add button handlers
    function setupAddButtonHandler(addBtn, customInput, customInputGroup, addFunction) {
        if (!addBtn || !customInput || !customInputGroup || !addFunction) return;

        addBtn.addEventListener('click', function() {
            const name = customInput.value.trim();
            if (name) {
                addFunction(name);
                customInput.value = '';
                customInputGroup.classList.add('d-none');
            }
        });

        // Also handle Enter key in the input field
        customInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                addBtn.click();
            }
        });
    }

    // Setup all dropdown handlers
    setupDropdownChangeHandler(prompterSubject, customSubjectInputGroup, customSubjectInput);
    setupDropdownChangeHandler(prompterAction, customActionInputGroup, customActionInput);
    setupDropdownChangeHandler(prompterEnvironment, customEnvironmentInputGroup, customEnvironmentInput);
    setupDropdownChangeHandler(prompterCamera, customCameraInputGroup, customCameraInput);
    setupDropdownChangeHandler(prompterTime, customTimeInputGroup, customTimeInput);
    setupDropdownChangeHandler(prompterMood, customMoodInputGroup, customMoodInput);
    setupDropdownChangeHandler(prompterStyle, customStyleInputGroup, customStyleInput);

    // Setup all cancel button handlers
    setupCancelButtonHandler(cancelCustomSubjectBtn, customSubjectInputGroup, customSubjectInput);
    setupCancelButtonHandler(cancelCustomActionBtn, customActionInputGroup, customActionInput);
    setupCancelButtonHandler(cancelCustomEnvironmentBtn, customEnvironmentInputGroup, customEnvironmentInput);
    setupCancelButtonHandler(cancelCustomCameraBtn, customCameraInputGroup, customCameraInput);
    setupCancelButtonHandler(cancelCustomTimeBtn, customTimeInputGroup, customTimeInput);
    setupCancelButtonHandler(cancelCustomMoodBtn, customMoodInputGroup, customMoodInput);
    setupCancelButtonHandler(cancelCustomStyleBtn, customStyleInputGroup, customStyleInput);

    // Setup all add button handlers
    setupAddButtonHandler(addCustomSubjectBtn, customSubjectInput, customSubjectInputGroup, addCustomSubject);
    setupAddButtonHandler(addCustomActionBtn, customActionInput, customActionInputGroup, addCustomAction);
    setupAddButtonHandler(addCustomEnvironmentBtn, customEnvironmentInput, customEnvironmentInputGroup, addCustomEnvironment);
    setupAddButtonHandler(addCustomCameraBtn, customCameraInput, customCameraInputGroup, addCustomCamera);
    setupAddButtonHandler(addCustomTimeBtn, customTimeInput, customTimeInputGroup, addCustomTime);
    setupAddButtonHandler(addCustomMoodBtn, customMoodInput, customMoodInputGroup, addCustomMood);
    setupAddButtonHandler(addCustomStyleBtn, customStyleInput, customStyleInputGroup, addCustomStyle);

    // Setup delete button handlers
    setupDeleteButtonHandlers('Subject', deleteCustomSubject);
    setupDeleteButtonHandlers('Action', deleteCustomAction);
    setupDeleteButtonHandlers('Environment', deleteCustomEnvironment);
    setupDeleteButtonHandlers('Camera', deleteCustomCamera);
    setupDeleteButtonHandlers('Time', deleteCustomTime);
    setupDeleteButtonHandlers('Mood', deleteCustomMood);
    setupDeleteButtonHandlers('Style', deleteCustomStyle);

    // Function to setup delete button handlers
    function setupDeleteButtonHandlers(type, deleteFunction) {
        const deleteBtn = document.getElementById(`deleteCustom${type}Btn`);
        const deleteSelect = document.getElementById(`deleteCustom${type}Select`);
        const deleteInputGroup = document.getElementById(`deleteCustom${type}InputGroup`);
        const cancelBtn = document.getElementById(`cancelDeleteCustom${type}Btn`);

        if (!deleteBtn || !deleteSelect || !deleteInputGroup || !cancelBtn) return;

        // Setup delete button click handler
        deleteBtn.addEventListener('click', function() {
            const selectedId = deleteSelect.value;
            if (selectedId) {
                deleteFunction(selectedId);
                deleteInputGroup.classList.add('d-none');
            } else {
                showToast('Please select an item to delete', 'warning');
            }
        });

        // Setup cancel button click handler
        cancelBtn.addEventListener('click', function() {
            deleteInputGroup.classList.add('d-none');
        });
    }

    // Handle export controls button
    if (exportControlsBtn) {
        exportControlsBtn.addEventListener('click', function() {
            exportControls();
        });
    }

    // Handle import controls button
    if (importControlsBtn) {
        importControlsBtn.addEventListener('click', function() {
            controlsFileInput.click();
        });

        controlsFileInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                importControls(this.files[0]);
            }
        });
    }

    // Export controls to JSON file
    function exportControls() {
        const data = {
            subjects: customSubjects,
            actions: customActions,
            environments: customEnvironments,
            cameras: customCameras,
            times: customTimes,
            moods: customMoods,
            styles: customStyles,
            version: '1.1',
            exportDate: new Date().toISOString()
        };

        const hasCustomItems =
            customSubjects.length > 0 ||
            customActions.length > 0 ||
            customEnvironments.length > 0 ||
            customCameras.length > 0 ||
            customTimes.length > 0 ||
            customMoods.length > 0 ||
            customStyles.length > 0;

        if (!hasCustomItems) {
            showToast('No custom items to export', 'warning');
            return;
        }

        // Create JSON string
        const jsonStr = JSON.stringify(data, null, 2);

        // Create a blob and download link
        const blob = new Blob([jsonStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        // Create download link and trigger click
        const a = document.createElement('a');
        a.href = url;
        a.download = 'prompter_controls_' + new Date().toISOString().slice(0, 10) + '.json';
        document.body.appendChild(a);
        a.click();

        // Clean up
        setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }, 100);

        showToast('Controls exported successfully!', 'success');
    }

    // Import controls from JSON file
    function importControls(file) {
        const reader = new FileReader();

        reader.onload = function(e) {
            try {
                const data = JSON.parse(e.target.result);

                // Import subjects
                if (data.subjects && Array.isArray(data.subjects)) {
                    customSubjects = data.subjects;
                    updateDropdown(prompterSubject, customSubjects);
                }

                // Import actions
                if (data.actions && Array.isArray(data.actions)) {
                    customActions = data.actions;
                    updateDropdown(prompterAction, customActions);
                }

                // Import environments
                if (data.environments && Array.isArray(data.environments)) {
                    customEnvironments = data.environments;
                    updateDropdown(prompterEnvironment, customEnvironments);
                }

                // Import cameras
                if (data.cameras && Array.isArray(data.cameras)) {
                    customCameras = data.cameras;
                    updateDropdown(prompterCamera, customCameras);
                }

                // Import times
                if (data.times && Array.isArray(data.times)) {
                    customTimes = data.times;
                    updateDropdown(prompterTime, customTimes);
                }

                // Import moods
                if (data.moods && Array.isArray(data.moods)) {
                    customMoods = data.moods;
                    updateDropdown(prompterMood, customMoods);
                }

                // Import styles
                if (data.styles && Array.isArray(data.styles)) {
                    customStyles = data.styles;
                    updateDropdown(prompterStyle, customStyles);
                }

                // Support for legacy format (v1.0)
                if (data.version === '1.0') {
                    if (data.categories && Array.isArray(data.categories)) {
                        // Convert old categories to subjects
                        customSubjects = data.categories.map(cat => ({
                            id: cat.id,
                            name: cat.name
                        }));
                        updateDropdown(prompterSubject, customSubjects);
                    }
                }

                // Save to localStorage
                saveCustomItems('csvision_custom_subjects', customSubjects);
                saveCustomItems('csvision_custom_actions', customActions);
                saveCustomItems('csvision_custom_environments', customEnvironments);
                saveCustomItems('csvision_custom_cameras', customCameras);
                saveCustomItems('csvision_custom_times', customTimes);
                saveCustomItems('csvision_custom_moods', customMoods);
                saveCustomItems('csvision_custom_styles', customStyles);

                // Setup delete buttons for imported custom items
                setupDeleteButtons();

                showToast('Controls imported successfully!', 'success');
            } catch (error) {
                console.error('Error importing controls:', error);
                showToast('Error importing controls. Invalid file format.', 'error');
            }
        };

        reader.readAsText(file);
    }

    // Load custom options on startup
    loadCustomOptions();

    // Setup delete buttons for custom items
    setupDeleteButtons();

    // Initialize Tabulator for prompt results
    let promptsTable = null;

    function initPrompterTable() {
        if (prompterTable) {
            promptsTable = new Tabulator(prompterTable, {
                layout: "fitColumns",
                selectable: true,
                placeholder: "No prompts generated yet",
                height: "100%", // Use full height of container
                pagination: true, // Enable pagination
                paginationSize: 10, // 10 rows per page
                paginationSizeSelector: [5, 10, 20, 50, 100], // Allow user to select rows per page
                movableColumns: true, // Allow column reordering
                columns: [
                    { title: "No.", formatter: "rownum", width: 60, headerSort: false },
                    {
                        title: "Prompt",
                        field: "prompt",
                        formatter: function(cell) {
                            const value = cell.getValue();
                            const rowData = cell.getRow().getData();
                            const isUsed = rowData.used || false;

                            // Create container with relative positioning for overlay
                            const container = document.createElement('div');
                            container.style.position = 'relative';
                            container.style.minHeight = '60px';
                            container.style.padding = '8px';
                            container.style.whiteSpace = 'pre-wrap';
                            container.style.wordBreak = 'break-word';
                            container.textContent = value;

                            // Add used indicator overlay if prompt has been used
                            if (isUsed) {
                                const overlay = document.createElement('div');
                                overlay.innerHTML = '<i class="bi bi-check-circle-fill"></i>';
                                overlay.style.position = 'absolute';
                                overlay.style.top = '4px';
                                overlay.style.right = '4px';
                                overlay.style.color = '#28a745';
                                overlay.style.fontSize = '18px';
                                overlay.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
                                overlay.style.borderRadius = '50%';
                                overlay.style.padding = '2px';
                                overlay.style.boxShadow = '0 1px 3px rgba(0,0,0,0.2)';
                                overlay.title = 'This prompt has been used';
                                container.appendChild(overlay);
                            }

                            return container;
                        },
                        headerSort: false,
                        resizable: true
                    },
                    {
                        title: "Actions",
                        formatter: function() {
                            return `
                                <button class="btn btn-sm btn-primary-purple text-white copy-prompt-btn bi bi-clipboard" title="Copy Prompt">
                                </button>
                                <button class="btn btn-sm btn-success send-to-imagen-btn bi bi-arrow-right-circle" title="Send to Imagen">
                                </button>
                            `;
                        },
                        width: 120,
                        headerSort: false,
                        hozAlign: "center",
                        cellClick: function(e, cell) {
                            const target = e.target.closest('button');
                            if (!target) return;

                            const rowData = cell.getRow().getData();

                            if (target.classList.contains('copy-prompt-btn')) {
                                copyPromptToClipboard(rowData.prompt, cell.getRow());
                            } else if (target.classList.contains('send-to-imagen-btn')) {
                                sendPromptToImagen(rowData.prompt, cell.getRow());
                            }
                        }
                    }
                ]
            });
        }
    }

    // Initialize the table
    initPrompterTable();

    // Initialize sliders and inputs
    if (prompterCountSlider && prompterCountInput && prompterCountValue) {
        // Sync slider and input for count
        prompterCountSlider.addEventListener('input', function() {
            const value = parseInt(this.value);
            prompterCountInput.value = value;
            prompterCountValue.textContent = value;
        });

        prompterCountInput.addEventListener('input', function() {
            let value = parseInt(this.value);

            // Enforce min/max constraints
            if (value < 1) value = 1;
            if (value > 1000) value = 1000;

            // Update slider if within its range
            if (value <= 20) {
                prompterCountSlider.value = value;
            }

            // Update display and input value
            prompterCountValue.textContent = value;
            this.value = value;
        });
    }

    if (prompterLengthSlider && prompterLengthInput && prompterLengthValue) {
        // Set initial values
        updateLengthDisplay(prompterLengthSlider.value);

        // Sync slider and input for length
        prompterLengthSlider.addEventListener('input', function() {
            const value = parseInt(this.value);
            prompterLengthInput.value = value;
            updateLengthDisplay(value);
        });

        prompterLengthInput.addEventListener('input', function() {
            let value = parseInt(this.value);

            // Enforce min/max constraints
            if (value < 25) value = 25;
            if (value > 1000) value = 1000;

            // Update slider if within its range
            prompterLengthSlider.value = value;
            updateLengthDisplay(value);

            // Update input value
            this.value = value;
        });
    }

    // Function to update length display text
    function updateLengthDisplay(value) {
        if (!prompterLengthValue) return;

        const wordCount = parseInt(value);
        prompterLengthValue.textContent = wordCount + ' words';
    }

    // Function to copy prompt to clipboard
    function copyPromptToClipboard(prompt, row) {
        navigator.clipboard.writeText(prompt)
            .then(() => {
                // Mark prompt as used
                if (row) {
                    const rowData = row.getData();
                    rowData.used = true;
                    row.update(rowData);
                }
                showToast('Prompt copied to clipboard!', 'success');
            })
            .catch(err => {
                console.error('Failed to copy prompt: ', err);
                showToast('Failed to copy prompt', 'error');
            });
    }

    // Function to send prompt to Imagen tab
    function sendPromptToImagen(prompt, row) {
        const imagenPrompt = document.getElementById('imagenPrompt');
        if (imagenPrompt) {
            imagenPrompt.value = prompt;
            // Mark prompt as used
            if (row) {
                const rowData = row.getData();
                rowData.used = true;
                row.update(rowData);
            }
            // Switch to Imagen tab
            document.getElementById('imagen-subtab').click();
            showToast('Prompt sent to Imagen tab!', 'success');
        } else {
            showToast('Imagen tab not found', 'error');
        }
    }

    // Function to export prompts to CSV
    if (exportPromptsBtn) {
        exportPromptsBtn.addEventListener('click', function() {
            if (promptsTable && promptsTable.getDataCount() > 0) {
                // Get all prompts data
                const promptsData = promptsTable.getData();

                // Create CSV header
                let csvContent = "ID,Prompt,Used\n";

                // Add each prompt as a row
                promptsData.forEach((row, index) => {
                    // Escape quotes in the prompt text
                    const escapedPrompt = row.prompt.replace(/"/g, '""');
                    const usedStatus = row.used ? 'Yes' : 'No';
                    // Add row to CSV content
                    csvContent += `${index + 1},"${escapedPrompt}","${usedStatus}"\n`;
                });

                // Create a blob and download link
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const url = URL.createObjectURL(blob);

                // Create download link and trigger click
                const a = document.createElement('a');
                a.href = url;
                a.download = 'prompts_export_' + new Date().toISOString().slice(0, 10) + '.csv';
                document.body.appendChild(a);
                a.click();

                // Clean up
                setTimeout(() => {
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }, 100);

                showToast('Prompts exported to CSV successfully!', 'success');
            } else {
                showToast('No prompts to export', 'warning');
            }
        });
    }

    // Function to copy all prompts
    if (copyAllPromptsBtn) {
        copyAllPromptsBtn.addEventListener('click', function() {
            if (promptsTable && promptsTable.getDataCount() > 0) {
                const promptsData = promptsTable.getData();
                const allPrompts = promptsData.map(row => row.prompt).join('\n\n---\n\n');
                navigator.clipboard.writeText(allPrompts)
                    .then(() => {
                        // Mark all prompts as used
                        promptsData.forEach(row => {
                            row.used = true;
                        });
                        promptsTable.setData(promptsData);
                        showToast('All prompts copied to clipboard!', 'success');
                    })
                    .catch(err => {
                        console.error('Failed to copy prompts: ', err);
                        showToast('Failed to copy prompts', 'error');
                    });
            } else {
                showToast('No prompts to copy', 'warning');
            }
        });
    }

    // Function to clear prompts
    if (clearPromptsBtn) {
        clearPromptsBtn.addEventListener('click', function() {
            if (promptsTable) {
                promptsTable.clearData();
                showToast('Prompts cleared', 'info');
            }
        });
    }

    // Function to compress image to thumbnail
    function compressImage(dataUrl, maxWidth = 512, maxHeight = 512, quality = 0.7) {
        return new Promise((resolve) => {
            const img = new Image();
            img.src = dataUrl;
            img.onload = function() {
                // Calculate new dimensions while maintaining aspect ratio
                let width = img.width;
                let height = img.height;

                if (width > height) {
                    if (width > maxWidth) {
                        height = Math.round(height * maxWidth / width);
                        width = maxWidth;
                    }
                } else {
                    if (height > maxHeight) {
                        width = Math.round(width * maxHeight / height);
                        height = maxHeight;
                    }
                }

                // Create canvas and draw resized image
                const canvas = document.createElement('canvas');
                canvas.width = width;
                canvas.height = height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0, width, height);

                // Convert to data URL with reduced quality
                const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);

                console.log(`Image compressed: Original size: ${Math.round(dataUrl.length/1024)}KB, Compressed size: ${Math.round(compressedDataUrl.length/1024)}KB`);
                resolve(compressedDataUrl);
            };
        });
    }

    // Function to handle image upload
    if (selectPrompterImageBtn && prompterImageInput) {
        selectPrompterImageBtn.addEventListener('click', function() {
            prompterImageInput.click();
        });

        prompterImageInput.addEventListener('change', async function() {
            if (this.files && this.files[0]) {
                const file = this.files[0];
                const reader = new FileReader();

                reader.onload = async function(e) {
                    // Store original image for display
                    const originalImage = e.target.result;
                    prompterSourceImage.src = originalImage;
                    prompterImagePreview.classList.remove('d-none');
                    prompterImagePlaceholder.classList.add('d-none');

                    // Compress image and store in data attribute for API calls
                    const compressedImage = await compressImage(originalImage);
                    prompterSourceImage.dataset.compressedSrc = compressedImage;

                    // Disable category when image is present
                    updateCategoryVisibility(true);
                };

                reader.readAsDataURL(file);
            }
        });
    }

    // Function to remove image
    if (removePrompterImageBtn) {
        removePrompterImageBtn.addEventListener('click', function() {
            prompterSourceImage.src = '';
            // Also clear the compressed image data
            if (prompterSourceImage.dataset.compressedSrc) {
                delete prompterSourceImage.dataset.compressedSrc;
            }
            prompterImagePreview.classList.add('d-none');
            prompterImagePlaceholder.classList.remove('d-none');

            // Enable category when no image is present
            updateCategoryVisibility(false);
        });
    }

    // Function to check if required fields are filled
    function validateStructuredPromptFields() {
        // All fields are now optional, so we always return true
        return true;
    }

    // Function to get the label text for a selected option
    function getSelectedOptionLabel(selectElement) {
        if (!selectElement || selectElement.selectedIndex === -1) return '';

        return selectElement.options[selectElement.selectedIndex].textContent;
    }

    // Function to get custom item by ID
    function getCustomItemById(items, itemId) {
        if (!items || !Array.isArray(items)) return null;
        return items.find(item => item.id === itemId);
    }

    // Function to get the value for a structured prompt element
    function getStructuredPromptElementValue(selectElement, customItems) {
        if (!selectElement || selectElement.value === '') return '';

        // If it's a custom value
        if (selectElement.value.startsWith('custom_')) {
            const itemId = selectElement.value.replace('custom_', '');
            const customItem = getCustomItemById(customItems, itemId);
            return customItem ? customItem.name : '';
        }

        // Return the label text for the selected option
        return getSelectedOptionLabel(selectElement);
    }

    // Function to generate prompts
    if (generatePromptsBtn) {
        generatePromptsBtn.addEventListener('click', async function() {
            // Validate required fields
            if (!validateStructuredPromptFields()) {
                return;
            }

            // Get API key
            const apiKey = await getApiKey();
            if (!apiKey) {
                showToast('Please add a Gemini API key in the Settings tab', 'error');
                return;
            }

            // Get content type from our state variable
            const contentType = currentContentType;

            // Get structured prompt elements
            const subject = getStructuredPromptElementValue(prompterSubject, customSubjects);
            const action = getStructuredPromptElementValue(prompterAction, customActions);
            const environment = getStructuredPromptElementValue(prompterEnvironment, customEnvironments);
            const camera = getStructuredPromptElementValue(prompterCamera, customCameras);
            const time = getStructuredPromptElementValue(prompterTime, customTimes);
            const mood = getStructuredPromptElementValue(prompterMood, customMoods);
            const style = getStructuredPromptElementValue(prompterStyle, customStyles);

            // Get parameters
            const params = {
                contentType: contentType, // Add content type (image or video)
                subject: subject,
                action: action,
                environment: environment,
                camera: camera,
                time: time,
                mood: mood,
                style: style,
                count: parseInt(prompterCountInput.value),
                length: getLengthFromInput(),
                instructions: prompterInstructions.value,
                // Use compressed image if available, otherwise use original
                imageData: prompterSourceImage.dataset.compressedSrc || prompterSourceImage.src || null
            };

            // Function to determine length value based on input
            function getLengthFromInput() {
                const wordCount = parseInt(prompterLengthInput.value);
                return wordCount.toString();
            }

            // Show loading
            prompterLoading.style.display = 'block';

            try {
                // If we have an image, analyze it first
                if (params.imageData) {
                    console.log("Analyzing image before generating prompts...");
                    const imageAnalysis = await analyzeImageWithGemini(apiKey, params.imageData);

                    if (imageAnalysis) {
                        // Add the image analysis to the instructions
                        if (params.instructions) {
                            params.instructions += `. Based on image analysis: ${imageAnalysis}`;
                        } else {
                            params.instructions = `Based on image analysis: ${imageAnalysis}`;
                        }
                        console.log("Added image analysis to instructions:", params.instructions);
                    }
                }

                // Generate prompts
                const prompts = await generatePromptsWithGemini(apiKey, params);

                // Hide loading
                prompterLoading.style.display = 'none';

                // Display results
                if (prompts && prompts.length > 0) {
                    const promptData = prompts.map(prompt => ({ prompt, used: false }));
                    promptsTable.setData(promptData);
                    showToast(`Generated ${prompts.length} prompts successfully!`, 'success');
                } else {
                    showToast('Failed to generate prompts', 'error');
                }
            } catch (error) {
                console.error('Error generating prompts:', error);
                prompterLoading.style.display = 'none';
                showToast(`Error: ${error.message}`, 'error');
            }
        });
    }

    // Function to generate prompts using Gemini API
    async function generatePromptsWithGemini(apiKey, params) {
        try {
            console.log("Generating prompts with params:", params);

            // Use the text generation model
            const model = "gemini-2.0-flash";
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`;

            // Build the system prompt based on structured parameters
            let systemPrompt = `Generate ${params.count} unique, detailed, and creative prompts for ${params.contentType} content`;

            // Add subject (optional)
            if (params.subject) {
                systemPrompt += ` featuring ${params.subject}`;
            }

            // Add action (optional)
            if (params.action) {
                systemPrompt += ` ${params.action}`;
            }

            // Add environment (optional)
            if (params.environment) {
                systemPrompt += ` in ${params.environment}`;
            }

            // Add camera work (optional)
            if (params.camera) {
                systemPrompt += `, captured with ${params.camera}`;
            }

            // Add time of day (optional)
            if (params.time) {
                systemPrompt += ` during ${params.time}`;
            }

            // Add mood/lighting (optional)
            if (params.mood) {
                systemPrompt += ` with ${params.mood} mood/lighting`;
            }

            // Add style (optional)
            if (params.style) {
                systemPrompt += ` in ${params.style} style`;
            }

            // Add length parameter
            if (params.length) {
                systemPrompt += `. Each prompt should be approximately ${params.length} words`;
            }

            // Add additional instructions
            if (params.instructions) {
                systemPrompt += `. Additional instructions: ${params.instructions}`;
            }

            systemPrompt += `. Format each prompt as a separate paragraph without numbering or bullet points.`;

            // Prepare the request body
            const requestBody = {
                contents: [{
                    role: "user",
                    parts: []
                }],
                generationConfig: {
                    temperature: 0.7,
                    topP: 0.95,
                    topK: 40,
                    maxOutputTokens: 8192
                },
                safetySettings: [
                    {
                        category: "HARM_CATEGORY_HARASSMENT",
                        threshold: "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        category: "HARM_CATEGORY_HATE_SPEECH",
                        threshold: "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                        threshold: "BLOCK_MEDIUM_AND_ABOVE"
                    },
                    {
                        category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                        threshold: "BLOCK_MEDIUM_AND_ABOVE"
                    }
                ]
            };

            // Add image if provided
            if (params.imageData && params.imageData.startsWith('data:image')) {
                const base64Data = params.imageData.split(',')[1];
                const mimeType = params.imageData.split(';')[0].split(':')[1];

                // Using the Google GenAI approach for image recognition
                requestBody.contents[0].parts.push({
                    inlineData: {
                        mimeType: mimeType,
                        data: base64Data
                    }
                });

                // Add text prompt for image
                requestBody.contents[0].parts.push({
                    text: `Based on this image, ${systemPrompt}`
                });

                console.log("Using image recognition for prompt generation");
            } else {
                // Text-only prompt
                requestBody.contents[0].parts.push({
                    text: systemPrompt
                });

                console.log("Using text-only prompt generation");
            }

            console.log("Sending request to Gemini API:", JSON.stringify(requestBody, null, 2));

            // Check if parallel processing is enabled
            const enableParallelProcessing = localStorage.getItem('csvision_enable_parallel') === 'true';
            console.log(`Prompter generation using parallel processing: ${enableParallelProcessing ? 'Enabled' : 'Disabled'}`);

            // Make API request
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error("API Error Response:", errorText);
                throw new Error(`API Error (${response.status}): ${errorText}`);
            }

            const responseData = await response.json();
            console.log("Gemini API Response:", responseData);

            // Process the results
            if (responseData.candidates &&
                responseData.candidates[0] &&
                responseData.candidates[0].content &&
                responseData.candidates[0].content.parts) {

                const text = responseData.candidates[0].content.parts
                    .filter(part => part.text)
                    .map(part => part.text)
                    .join(' ');

                // Split the text into separate prompts
                // We'll use paragraph breaks as separators
                const prompts = text.split(/\n\s*\n/)
                    .map(p => p.trim())
                    .filter(p => p.length > 0);

                // If we don't have enough prompts, try to split by newlines
                if (prompts.length < params.count) {
                    const moreSplits = text.split(/\n/)
                        .map(p => p.trim())
                        .filter(p => p.length > 0 && !p.startsWith('*') && !p.startsWith('-'));

                    if (moreSplits.length > prompts.length) {
                        return moreSplits.slice(0, params.count);
                    }
                }

                // Limit to the requested count
                return prompts.slice(0, params.count);
            }

            throw new Error('Invalid response format from Gemini API');
        } catch (error) {
            console.error('Error in generatePromptsWithGemini:', error);
            throw error;
        }
    }

    // Function to analyze image using Google GenAI
    async function analyzeImageWithGemini(apiKey, imageData) {
        try {
            if (!imageData || !imageData.startsWith('data:image')) {
                return null;
            }

            console.log("Analyzing image with Gemini...");

            // Extract base64 data and mime type
            const base64Data = imageData.split(',')[1];
            const mimeType = imageData.split(';')[0].split(':')[1];

            // Use the Gemini model for image understanding
            const model = "gemini-2.0-flash";
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`;

            // Prepare the request body for image analysis
            const requestBody = {
                contents: [{
                    parts: [
                        {
                            text: "Analyze this image and describe what you see. Focus on the main subject, style, setting, and any notable elements."
                        },
                        {
                            inlineData: {
                                mimeType: mimeType,
                                data: base64Data
                            }
                        }
                    ]
                }],
                generationConfig: {
                    temperature: 0.2,
                    maxOutputTokens: 1024
                }
            };

            // Make API request
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error("Image Analysis API Error Response:", errorText);
                return null;
            }

            const responseData = await response.json();
            console.log("Image Analysis Response:", responseData);

            // Extract the analysis text
            if (responseData.candidates &&
                responseData.candidates[0] &&
                responseData.candidates[0].content &&
                responseData.candidates[0].content.parts) {

                const analysisText = responseData.candidates[0].content.parts
                    .filter(part => part.text)
                    .map(part => part.text)
                    .join(' ');

                console.log("Image Analysis Result:", analysisText);
                return analysisText;
            }

            return null;
        } catch (error) {
            console.error("Error analyzing image:", error);
            return null;
        }
    }

    // Function to get API key
    async function getApiKey() {
        return new Promise((resolve) => {
            // Try to get it from the global window object first
            try {
                if (typeof window.getApiKey === 'function') {
                    const apiKey = window.getApiKey();
                    if (apiKey) {
                        console.log("Using API key from window.getApiKey");
                        return resolve(apiKey);
                    }
                }
            } catch (e) {
                console.error("Error accessing window.getApiKey:", e);
            }

            // Check if API key rotation is enabled
            const enableApiKeyRotation = localStorage.getItem('csvision_enable_api_key_rotation') === 'true';

            // If rotation is enabled and we have a getNextApiKey function, try to use it
            if (enableApiKeyRotation && typeof window.getNextApiKey === 'function') {
                try {
                    const nextKey = window.getNextApiKey();
                    if (nextKey) {
                        console.log("Using next API key from rotation for prompter generation");
                        return resolve(nextKey);
                    }
                } catch (e) {
                    console.error("Error accessing window.getNextApiKey:", e);
                }
            }

            // Try to get API key from localStorage directly
            const apiKey = localStorage.getItem('csvision_api_key');
            if (apiKey) {
                console.log("Using API key from localStorage csvision_api_key");
                return resolve(apiKey);
            }

            // Last resort - try the old apiKeys array if it exists
            const apiKeys = JSON.parse(localStorage.getItem('csvision_api_keys') || '[]');
            if (apiKeys.length > 0) {
                const activeKey = apiKeys.find(key => key.active);
                if (activeKey) {
                    console.log("Using active API key from csvision_api_keys");
                    return resolve(activeKey.key);
                } else if (apiKeys[0]) {
                    console.log("Using first API key from csvision_api_keys");
                    return resolve(apiKeys[0].key);
                }
            }

            // Try the legacy apiKeys format as a last resort
            const legacyApiKeys = JSON.parse(localStorage.getItem('apiKeys') || '[]');
            if (legacyApiKeys.length > 0) {
                console.log("Using API key from legacy apiKeys format");
                return resolve(legacyApiKeys[0].key);
            }

            console.warn("No API key found in any storage location");
            resolve(null);
        });
    }

    // Function to show toast notification - using global toast manager
    function showToast(message, type = 'info') {
        // Use the global showToast function from toast-manager.js
        if (window.showToast) {
            return window.showToast(message, type === 'info' ? 'purple' : type);
        }
    }

    // Function to update category visibility when image is present
    function updateCategoryVisibility(hasImage) {
        // With all fields being optional, we don't need to disable any fields
        // This function is kept for backward compatibility
        console.log(`Image present: ${hasImage}. All fields remain enabled as they are all optional.`);
    }

    // Function to handle image drop from Imagen to Prompter
    window.sendImageToPrompter = async function(imageSrc) {
        if (prompterSourceImage && imageSrc) {
            // Store original image for display
            prompterSourceImage.src = imageSrc;
            prompterImagePreview.classList.remove('d-none');
            prompterImagePlaceholder.classList.add('d-none');

            // Compress image and store in data attribute for API calls
            const compressedImage = await compressImage(imageSrc);
            prompterSourceImage.dataset.compressedSrc = compressedImage;

            // Disable category when image is present
            updateCategoryVisibility(true);

            // Switch to Prompter tab
            document.getElementById('prompter-subtab').click();

            showToast('Image sent to Prompter tab!', 'success');
        }
    };
});
